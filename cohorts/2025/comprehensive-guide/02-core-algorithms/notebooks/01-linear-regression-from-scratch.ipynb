{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 📈 Linear Regression from Scratch: Mathematical Foundations to Implementation\n", "\n", "> **Master linear regression through mathematical derivation and hands-on implementation**\n", "\n", "This notebook provides a complete journey through linear regression - from mathematical foundations to practical implementation and real-world applications."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from typing import Tuple, Optional\n", "import pandas as pd\n", "from sklearn.datasets import make_regression, load_boston\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "np.random.seed(42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Learning Objectives\n", "\n", "By the end of this notebook, you will:\n", "- Derive linear regression from first principles\n", "- Understand the geometric interpretation of least squares\n", "- Implement multiple solution methods (normal equation, gradient descent)\n", "- Analyze assumptions and limitations of linear regression\n", "- Apply linear regression to real-world problems"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📐 Part 1: Mathematical Foundation\n", "\n", "### The Linear Model\n", "\n", "Linear regression assumes a linear relationship between features and target:\n", "\n", "$$y = \\beta_0 + \\beta_1 x_1 + \\beta_2 x_2 + ... + \\beta_p x_p + \\epsilon$$\n", "\n", "In matrix form:\n", "$$\\mathbf{y} = \\mathbf{X}\\boldsymbol{\\beta} + \\boldsymbol{\\epsilon}$$\n", "\n", "Where:\n", "- $\\mathbf{y}$ is the target vector (n × 1)\n", "- $\\mathbf{X}$ is the feature matrix (n × p)\n", "- $\\boldsymbol{\\beta}$ is the parameter vector (p × 1)\n", "- $\\boldsymbol{\\epsilon}$ is the error vector (n × 1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Least Squares Derivation\n", "\n", "We want to minimize the sum of squared errors:\n", "\n", "$$\\text{SSE} = \\sum_{i=1}^{n} (y_i - \\hat{y}_i)^2 = \\sum_{i=1}^{n} (y_i - \\mathbf{x}_i^T\\boldsymbol{\\beta})^2$$\n", "\n", "In matrix form:\n", "$$\\text{SSE} = (\\mathbf{y} - \\mathbf{X}\\boldsymbol{\\beta})^T(\\mathbf{y} - \\mathbf{X}\\boldsymbol{\\beta})$$\n", "\n", "Taking the derivative with respect to $\\boldsymbol{\\beta}$ and setting to zero:\n", "\n", "$$\\frac{\\partial \\text{SSE}}{\\partial \\boldsymbol{\\beta}} = -2\\mathbf{X}^T\\mathbf{y} + 2\\mathbf{X}^T\\mathbf{X}\\boldsymbol{\\beta} = 0$$\n", "\n", "Solving for $\\boldsymbol{\\beta}$:\n", "$$\\boldsymbol{\\hat{\\beta}} = (\\mathbf{X}^T\\mathbf{X})^{-1}\\mathbf{X}^T\\mathbf{y}$$\n", "\n", "This is the **Normal Equation**!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class LinearRegressionFromScratch:\n", "    \"\"\"Linear Regression implemented from scratch with multiple solution methods\"\"\"\n", "    \n", "    def __init__(self, method='normal_equation', learning_rate=0.01, max_iterations=1000, tolerance=1e-6):\n", "        \"\"\"\n", "        Initialize Linear Regression\n", "        \n", "        Parameters:\n", "        -----------\n", "        method : str\n", "            Solution method: 'normal_equation' or 'gradient_descent'\n", "        learning_rate : float\n", "            Learning rate for gradient descent\n", "        max_iterations : int\n", "            Maximum iterations for gradient descent\n", "        tolerance : float\n", "            Convergence tolerance for gradient descent\n", "        \"\"\"\n", "        self.method = method\n", "        self.learning_rate = learning_rate\n", "        self.max_iterations = max_iterations\n", "        self.tolerance = tolerance\n", "        \n", "        # Model parameters\n", "        self.weights = None\n", "        self.bias = None\n", "        \n", "        # Training history\n", "        self.cost_history = []\n", "        self.weight_history = []\n", "    \n", "    def _add_bias_column(self, X):\n", "        \"\"\"Add bias column (column of ones) to feature matrix\"\"\"\n", "        return np.column_stack([np.ones(X.shape[0]), X])\n", "    \n", "    def _compute_cost(self, X, y, weights):\n", "        \"\"\"Compute mean squared error cost\"\"\"\n", "        predictions = X @ weights\n", "        errors = y - predictions\n", "        cost = np.mean(errors ** 2)\n", "        return cost\n", "    \n", "    def _normal_equation(self, X, y):\n", "        \"\"\"Solve using normal equation: β = (X^T X)^(-1) X^T y\"\"\"\n", "        try:\n", "            # Add bias column\n", "            X_with_bias = self._add_bias_column(X)\n", "            \n", "            # Normal equation\n", "            XTX = X_with_bias.T @ X_with_bias\n", "            XTy = X_with_bias.T @ y\n", "            \n", "            # Check if matrix is invertible\n", "            if np.linalg.det(XTX) == 0:\n", "                raise np.linalg.LinAlg<PERSON>rror(\"Matrix is singular\")\n", "            \n", "            weights = np.linalg.solve(XTX, XTy)\n", "            \n", "            self.bias = weights[0]\n", "            self.weights = weights[1:]\n", "            \n", "            # Compute final cost\n", "            final_cost = self._compute_cost(X_with_bias, y, weights)\n", "            self.cost_history = [final_cost]\n", "            \n", "        except np.linalg.LinAlgError:\n", "            print(\"Warning: Matrix is singular. Using pseudo-inverse.\")\n", "            weights = np.linalg.pinv(X_with_bias) @ y\n", "            self.bias = weights[0]\n", "            self.weights = weights[1:]\n", "    \n", "    def _gradient_descent(self, X, y):\n", "        \"\"\"Solve using gradient descent\"\"\"\n", "        # Add bias column\n", "        X_with_bias = self._add_bias_column(X)\n", "        n_samples, n_features = X_with_bias.shape\n", "        \n", "        # Initialize weights randomly\n", "        weights = np.random.normal(0, 0.01, n_features)\n", "        \n", "        self.cost_history = []\n", "        self.weight_history = []\n", "        \n", "        for iteration in range(self.max_iterations):\n", "            # Forward pass\n", "            predictions = X_with_bias @ weights\n", "            errors = predictions - y\n", "            \n", "            # Compute cost\n", "            cost = np.mean(errors ** 2)\n", "            self.cost_history.append(cost)\n", "            self.weight_history.append(weights.copy())\n", "            \n", "            # Compute gradients\n", "            gradients = (2 / n_samples) * X_with_bias.T @ errors\n", "            \n", "            # Update weights\n", "            new_weights = weights - self.learning_rate * gradients\n", "            \n", "            # Check convergence\n", "            if np.linalg.norm(new_weights - weights) < self.tolerance:\n", "                print(f\"Converged after {iteration + 1} iterations\")\n", "                break\n", "            \n", "            weights = new_weights\n", "        \n", "        self.bias = weights[0]\n", "        self.weights = weights[1:]\n", "    \n", "    def fit(self, X, y):\n", "        \"\"\"Fit the linear regression model\"\"\"\n", "        X = np.array(X)\n", "        y = np.array(y)\n", "        \n", "        if self.method == 'normal_equation':\n", "            self._normal_equation(X, y)\n", "        elif self.method == 'gradient_descent':\n", "            self._gradient_descent(X, y)\n", "        else:\n", "            raise ValueError(\"Method must be 'normal_equation' or 'gradient_descent'\")\n", "        \n", "        return self\n", "    \n", "    def predict(self, X):\n", "        \"\"\"Make predictions\"\"\"\n", "        if self.weights is None:\n", "            raise ValueError(\"Model must be fitted before making predictions\")\n", "        \n", "        X = np.array(X)\n", "        return X @ self.weights + self.bias\n", "    \n", "    def score(self, X, y):\n", "        \"\"\"Compute R² score\"\"\"\n", "        y_pred = self.predict(X)\n", "        ss_res = np.sum((y - y_pred) ** 2)\n", "        ss_tot = np.sum((y - np.mean(y)) ** 2)\n", "        return 1 - (ss_res / ss_tot)\n", "\n", "# Test with simple 1D example\n", "print(\"Testing Linear Regression Implementation\")\n", "print(\"=\" * 40)\n", "\n", "# Generate simple dataset\n", "np.random.seed(42)\n", "X_simple = np.random.randn(100, 1)\n", "y_simple = 3 * X_simple.flatten() + 2 + 0.5 * np.random.randn(100)\n", "\n", "# Test both methods\n", "models = {\n", "    'Normal Equation': LinearRegressionFromScratch(method='normal_equation'),\n", "    'Gradient Descent': LinearRegressionFromScratch(method='gradient_descent', learning_rate=0.1)\n", "}\n", "\n", "for name, model in models.items():\n", "    model.fit(X_simple, y_simple)\n", "    r2 = model.score(X_simple, y_simple)\n", "    print(f\"{name}:\")\n", "    print(f\"  Weight: {model.weights[0]:.4f} (true: 3.0)\")\n", "    print(f\"  Bias: {model.bias:.4f} (true: 2.0)\")\n", "    print(f\"  R² Score: {r2:.4f}\")\n", "    print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Part 2: Visualization and Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_linear_regression(X, y, model, title=\"Linear Regression\"):\n", "    \"\"\"Visualize linear regression results\"\"\"\n", "    if X.shape[1] != 1:\n", "        print(\"Visualization only supports 1D features\")\n", "        return\n", "    \n", "    fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # Plot 1: Data and regression line\n", "    X_flat = X.flatten()\n", "    axes[0].scatter(X_flat, y, alpha=0.6, s=50, label='Data points')\n", "    \n", "    # Plot regression line\n", "    X_line = np.linspace(X_flat.min(), X_flat.max(), 100).reshape(-1, 1)\n", "    y_line = model.predict(X_line)\n", "    axes[0].plot(X_line, y_line, 'r-', linewidth=2, label='Regression line')\n", "    \n", "    # Plot residuals\n", "    y_pred = model.predict(X)\n", "    for i in range(len(X_flat)):\n", "        axes[0].plot([X_flat[i], X_flat[i]], [y[i], y_pred[i]], 'k--', alpha=0.3, linewidth=1)\n", "    \n", "    axes[0].set_xlabel('Feature')\n", "    axes[0].set_ylabel('Target')\n", "    axes[0].set_title(f'{title}\\nWeight: {model.weights[0]:.3f}, Bias: {model.bias:.3f}')\n", "    axes[0].legend()\n", "    axes[0].grid(True, alpha=0.3)\n", "    \n", "    # Plot 2: Cost history (if available)\n", "    if len(model.cost_history) > 1:\n", "        axes[1].plot(model.cost_history, 'b-', linewidth=2)\n", "        axes[1].set_xlabel('Iteration')\n", "        axes[1].set_ylabel('Cost (MSE)')\n", "        axes[1].set_title('Cost Function Convergence')\n", "        axes[1].grid(True, alpha=0.3)\n", "        axes[1].set_yscale('log')\n", "    else:\n", "        # Show residual plot instead\n", "        residuals = y - y_pred\n", "        axes[1].scatter(y_pred, residuals, alpha=0.6, s=50)\n", "        axes[1].axhline(y=0, color='r', linestyle='--', linewidth=2)\n", "        axes[1].set_xlabel('Predicted Values')\n", "        axes[1].set_ylabel('Residuals')\n", "        axes[1].set_title('Residual Plot')\n", "        axes[1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Visualize both methods\n", "for name, model in models.items():\n", "    visualize_linear_regression(X_simple, y_simple, model, name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔍 Part 3: Gradient Descent Deep Dive"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_gradient_descent_path(X, y, learning_rates=[0.01, 0.1, 0.5]):\n", "    \"\"\"Visualize how different learning rates affect gradient descent convergence\"\"\"\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    \n", "    # Generate 2D parameter space for visualization\n", "    if X.shape[1] == 1:\n", "        # For 1D features, we can visualize weight and bias\n", "        X_with_bias = np.column_stack([np.ones(X.shape[0]), X])\n", "        \n", "        # True parameters (from normal equation)\n", "        true_params = np.linalg.solve(X_with_bias.T @ X_with_bias, X_with_bias.T @ y)\n", "        \n", "        # Create parameter grid\n", "        bias_range = np.linspace(true_params[0] - 2, true_params[0] + 2, 50)\n", "        weight_range = np.linspace(true_params[1] - 2, true_params[1] + 2, 50)\n", "        B, W = np.meshgrid(bias_range, weight_range)\n", "        \n", "        # Compute cost surface\n", "        costs = np.zeros_like(B)\n", "        for i in range(B.shape[0]):\n", "            for j in range(B.shape[1]):\n", "                params = np.array([B[i, j], W[i, j]])\n", "                predictions = X_with_bias @ params\n", "                costs[i, j] = np.mean((y - predictions) ** 2)\n", "        \n", "        # Plot cost surface\n", "        contour = axes[0, 0].contour(B, W, costs, levels=20, alpha=0.6)\n", "        axes[0, 0].clabel(contour, inline=True, fontsize=8)\n", "        axes[0, 0].set_xlabel('Bias')\n", "        axes[0, 0].set_ylabel('Weight')\n", "        axes[0, 0].set_title('Cost Function Surface')\n", "        \n", "        # Plot true optimum\n", "        axes[0, 0].plot(true_params[0], true_params[1], 'r*', markersize=15, label='True Optimum')\n", "        axes[0, 0].legend()\n", "        \n", "        # Test different learning rates\n", "        colors = ['blue', 'green', 'orange']\n", "        \n", "        for idx, lr in enumerate(learning_rates):\n", "            model = LinearRegressionFromScratch(\n", "                method='gradient_descent', \n", "                learning_rate=lr, \n", "                max_iterations=100\n", "            )\n", "            model.fit(X, y)\n", "            \n", "            # Plot convergence path\n", "            if model.weight_history:\n", "                biases = [w[0] for w in model.weight_history]\n", "                weights = [w[1] for w in model.weight_history]\n", "                \n", "                axes[0, 0].plot(biases, weights, 'o-', color=colors[idx], \n", "                               alpha=0.7, markersize=3, label=f'LR={lr}')\n", "            \n", "            # Plot cost history\n", "            axes[0, 1].plot(model.cost_history, color=colors[idx], \n", "                           linewidth=2, label=f'LR={lr}')\n", "        \n", "        axes[0, 0].legend()\n", "        axes[0, 1].set_xlabel('Iteration')\n", "        axes[0, 1].set_ylabel('Cost')\n", "        axes[0, 1].set_title('Cost Convergence')\n", "        axes[0, 1].set_yscale('log')\n", "        axes[0, 1].legend()\n", "        axes[0, 1].grid(True, alpha=0.3)\n", "    \n", "    # Learning rate analysis\n", "    lr_range = np.logspace(-3, 0, 20)\n", "    final_costs = []\n", "    iterations_to_converge = []\n", "    \n", "    for lr in lr_range:\n", "        model = LinearRegressionFromScratch(\n", "            method='gradient_descent', \n", "            learning_rate=lr, \n", "            max_iterations=1000,\n", "            tolerance=1e-8\n", "        )\n", "        model.fit(X, y)\n", "        final_costs.append(model.cost_history[-1])\n", "        iterations_to_converge.append(len(model.cost_history))\n", "    \n", "    axes[1, 0].semilogx(lr_range, final_costs, 'bo-', linewidth=2, markersize=6)\n", "    axes[1, 0].set_xlabel('Learning Rate')\n", "    axes[1, 0].set_ylabel('Final Cost')\n", "    axes[1, 0].set_title('Learning Rate vs Final Cost')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    \n", "    axes[1, 1].semilogx(lr_range, iterations_to_converge, 'ro-', linewidth=2, markersize=6)\n", "    axes[1, 1].set_xlabel('Learning Rate')\n", "    axes[1, 1].set_ylabel('Iterations to Converge')\n", "    axes[1, 1].set_title('Learning Rate vs Convergence Speed')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Visualize gradient descent behavior\n", "visualize_gradient_descent_path(X_simple, y_simple)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🏠 Part 4: Real-World Application"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load a real dataset\n", "from sklearn.datasets import fetch_california_housing\n", "\n", "# Load California housing dataset\n", "housing = fetch_california_housing()\n", "X_housing = housing.data\n", "y_housing = housing.target\n", "feature_names = housing.feature_names\n", "\n", "print(\"California Housing Dataset\")\n", "print(f\"Features: {feature_names}\")\n", "print(f\"Shape: {X_housing.shape}\")\n", "print(f\"Target: Median house value (in hundreds of thousands of dollars)\")\n", "\n", "# Split the data\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X_housing, y_housing, test_size=0.2, random_state=42\n", ")\n", "\n", "# Scale the features for better gradient descent performance\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "print(f\"\\nTraining set: {X_train_scaled.shape}\")\n", "print(f\"Test set: {X_test_scaled.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare our implementation with scikit-learn\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "\n", "# Our implementations\n", "our_models = {\n", "    'Our Normal Equation': LinearRegressionFromScratch(method='normal_equation'),\n", "    'Our Gradient Descent': LinearRegressionFromScratch(\n", "        method='gradient_descent', \n", "        learning_rate=0.01, \n", "        max_iterations=1000\n", "    )\n", "}\n", "\n", "# Scikit-learn model\n", "sklearn_model = LinearRegression()\n", "\n", "print(\"Model Comparison on California Housing Dataset\")\n", "print(\"=\" * 50)\n", "\n", "# Train and evaluate our models\n", "results = {}\n", "\n", "for name, model in our_models.items():\n", "    # Train\n", "    model.fit(X_train_scaled, y_train)\n", "    \n", "    # Predict\n", "    y_pred_train = model.predict(X_train_scaled)\n", "    y_pred_test = model.predict(X_test_scaled)\n", "    \n", "    # Evaluate\n", "    train_mse = mean_squared_error(y_train, y_pred_train)\n", "    test_mse = mean_squared_error(y_test, y_pred_test)\n", "    train_r2 = r2_score(y_train, y_pred_train)\n", "    test_r2 = r2_score(y_test, y_pred_test)\n", "    \n", "    results[name] = {\n", "        'train_mse': train_mse,\n", "        'test_mse': test_mse,\n", "        'train_r2': train_r2,\n", "        'test_r2': test_r2\n", "    }\n", "    \n", "    print(f\"{name}:\")\n", "    print(f\"  Train MSE: {train_mse:.4f}\")\n", "    print(f\"  Test MSE:  {test_mse:.4f}\")\n", "    print(f\"  Train R²:  {train_r2:.4f}\")\n", "    print(f\"  Test R²:   {test_r2:.4f}\")\n", "    print()\n", "\n", "# Train scikit-learn model\n", "sklearn_model.fit(X_train_scaled, y_train)\n", "y_pred_train_sk = sklearn_model.predict(X_train_scaled)\n", "y_pred_test_sk = sklearn_model.predict(X_test_scaled)\n", "\n", "train_mse_sk = mean_squared_error(y_train, y_pred_train_sk)\n", "test_mse_sk = mean_squared_error(y_test, y_pred_test_sk)\n", "train_r2_sk = r2_score(y_train, y_pred_train_sk)\n", "test_r2_sk = r2_score(y_test, y_pred_test_sk)\n", "\n", "print(\"Scikit-learn LinearRegression:\")\n", "print(f\"  Train MSE: {train_mse_sk:.4f}\")\n", "print(f\"  Test MSE:  {test_mse_sk:.4f}\")\n", "print(f\"  Train R²:  {train_r2_sk:.4f}\")\n", "print(f\"  Test R²:   {test_r2_sk:.4f}\")\n", "\n", "# Check if our implementations match scikit-learn\n", "print(\"\\nParameter Comparison:\")\n", "print(f\"Scikit-learn weights: {sklearn_model.coef_[:3]}... (showing first 3)\")\n", "print(f\"Our normal eq weights: {our_models['Our Normal Equation'].weights[:3]}...\")\n", "print(f\"Our gradient desc weights: {our_models['Our Gradient Descent'].weights[:3]}...\")\n", "\n", "print(f\"\\nScikit-learn bias: {sklearn_model.intercept_:.4f}\")\n", "print(f\"Our normal eq bias: {our_models['Our Normal Equation'].bias:.4f}\")\n", "print(f\"Our gradient desc bias: {our_models['Our Gradient Descent'].bias:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔍 Part 5: Model Analysis and Diagnostics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_linear_regression(model, X, y, feature_names=None):\n", "    \"\"\"Comprehensive analysis of linear regression model\"\"\"\n", "    \n", "    # Predictions and residuals\n", "    y_pred = model.predict(X)\n", "    residuals = y - y_pred\n", "    \n", "    # Create subplots\n", "    fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "    \n", "    # 1. Predicted vs Actual\n", "    axes[0, 0].scatter(y, y_pred, alpha=0.6, s=20)\n", "    min_val, max_val = min(y.min(), y_pred.min()), max(y.max(), y_pred.max())\n", "    axes[0, 0].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)\n", "    axes[0, 0].set_xlabel('Actual Values')\n", "    axes[0, 0].set_ylabel('Predicted Values')\n", "    axes[0, 0].set_title('Predicted vs Actual')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    \n", "    # 2. <PERSON><PERSON><PERSON><PERSON> vs Predicted\n", "    axes[0, 1].scatter(y_pred, residuals, alpha=0.6, s=20)\n", "    axes[0, 1].axhline(y=0, color='r', linestyle='--', linewidth=2)\n", "    axes[0, 1].set_xlabel('Predicted Values')\n", "    axes[0, 1].set_ylabel('Residuals')\n", "    axes[0, 1].set_title('Residuals vs Predicted')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    \n", "    # 3. Residual histogram\n", "    axes[0, 2].hist(residuals, bins=30, alpha=0.7, density=True)\n", "    axes[0, 2].set_xlabel('Residuals')\n", "    axes[0, 2].set_ylabel('Density')\n", "    axes[0, 2].set_title('Residual Distribution')\n", "    axes[0, 2].grid(True, alpha=0.3)\n", "    \n", "    # Add normal distribution overlay\n", "    x_norm = np.linspace(residuals.min(), residuals.max(), 100)\n", "    y_norm = (1/np.sqrt(2*np.pi*np.var(residuals))) * np.exp(-0.5*((x_norm-np.mean(residuals))/np.std(residuals))**2)\n", "    axes[0, 2].plot(x_norm, y_norm, 'r-', linewidth=2, label='Normal')\n", "    axes[0, 2].legend()\n", "    \n", "    # 4. Q-Q plot for normality\n", "    from scipy import stats\n", "    stats.probplot(residuals, dist=\"norm\", plot=axes[1, 0])\n", "    axes[1, 0].set_title('Q-Q Plot (Normality Check)')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    \n", "    # 5. Feature importance (weights)\n", "    if feature_names is not None and len(feature_names) == len(model.weights):\n", "        importance = np.abs(model.weights)\n", "        sorted_idx = np.argsort(importance)[::-1]\n", "        \n", "        axes[1, 1].barh(range(len(importance)), importance[sorted_idx])\n", "        axes[1, 1].set_yticks(range(len(importance)))\n", "        axes[1, 1].set_yticklabels([feature_names[i] for i in sorted_idx])\n", "        axes[1, 1].set_xlabel('|Weight|')\n", "        axes[1, 1].set_title('Feature Importance (|Weights|)')\n", "        axes[1, 1].grid(True, alpha=0.3)\n", "    else:\n", "        axes[1, 1].bar(range(len(model.weights)), model.weights)\n", "        axes[1, 1].set_xlabel('Feature Index')\n", "        axes[1, 1].set_ylabel('Weight')\n", "        axes[1, 1].set_title('Model Weights')\n", "        axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    # 6. Learning curve (if available)\n", "    if hasattr(model, 'cost_history') and len(model.cost_history) > 1:\n", "        axes[1, 2].plot(model.cost_history, 'b-', linewidth=2)\n", "        axes[1, 2].set_xlabel('Iteration')\n", "        axes[1, 2].set_ylabel('Cost (MSE)')\n", "        axes[1, 2].set_title('Learning Curve')\n", "        axes[1, 2].set_yscale('log')\n", "        axes[1, 2].grid(True, alpha=0.3)\n", "    else:\n", "        # Show residuals vs feature index\n", "        axes[1, 2].scatter(range(len(residuals)), residuals, alpha=0.6, s=20)\n", "        axes[1, 2].axhline(y=0, color='r', linestyle='--', linewidth=2)\n", "        axes[1, 2].set_xlabel('Sample Index')\n", "        axes[1, 2].set_ylabel('Residuals')\n", "        axes[1, 2].set_title('Residuals vs Sample Index')\n", "        axes[1, 2].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Print diagnostic statistics\n", "    print(\"Model Diagnostics:\")\n", "    print(f\"  Mean Squared Error: {np.mean(residuals**2):.4f}\")\n", "    print(f\"  Root Mean Squared Error: {np.sqrt(np.mean(residuals**2)):.4f}\")\n", "    print(f\"  Mean Absolute Error: {np.mean(np.abs(residuals)):.4f}\")\n", "    print(f\"  R² Score: {model.score(X, y):.4f}\")\n", "    print(f\"  Residual Standard Deviation: {np.std(residuals):.4f}\")\n", "    \n", "    # Normality test\n", "    _, p_value = stats.shapiro(residuals[:5000] if len(residuals) > 5000 else residuals)\n", "    print(f\"  Shapiro-Wilk p-value (normality): {p_value:.4f}\")\n", "    if p_value > 0.05:\n", "        print(\"    → Residuals appear normally distributed\")\n", "    else:\n", "        print(\"    → Residuals may not be normally distributed\")\n", "\n", "# Analyze our best model\n", "best_model = our_models['Our Normal Equation']\n", "analyze_linear_regression(best_model, X_test_scaled, y_test, feature_names)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 <PERSON><PERSON><PERSON> and Key Takeaways\n", "\n", "### What We've Accomplished\n", "\n", "1. **Mathematical Foundation**: Derived linear regression from first principles\n", "2. **Multiple Solutions**: Implemented both normal equation and gradient descent\n", "3. **Practical Application**: Applied to real-world housing price prediction\n", "4. **Model Analysis**: Comprehensive diagnostics and validation\n", "\n", "### Key Insights\n", "\n", "- **Normal Equation**: Exact solution, but requires matrix inversion (O(n³))\n", "- **Gradient Descent**: Iterative solution, scales better to large datasets\n", "- **Feature Scaling**: Critical for gradient descent convergence\n", "- **Assumptions**: Linear relationship, independence, homoscedasticity, normality\n", "\n", "### When to Use Linear Regression\n", "\n", "✅ **Good for**:\n", "- Linear relationships between features and target\n", "- Interpretable models with clear feature importance\n", "- Baseline models for comparison\n", "- Small to medium datasets\n", "\n", "❌ **Not ideal for**:\n", "- Non-linear relationships\n", "- High-dimensional data without regularization\n", "- Categorical targets (use logistic regression instead)\n", "\n", "### Next Steps\n", "\n", "1. **Regularization**: Ridge, Lasso, Elastic Net\n", "2. **Polynomial Features**: Handling non-linear relationships\n", "3. **Robust Regression**: Dealing with outliers\n", "4. **Bayesian Linear Regression**: Uncertainty quantification\n", "\n", "**Remember**: Linear regression is simple but powerful. Understanding it deeply provides the foundation for more complex algorithms!"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}