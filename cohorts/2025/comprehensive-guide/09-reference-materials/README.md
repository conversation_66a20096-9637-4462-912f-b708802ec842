# 📚 Reference Materials & Quick Guides

> **Your comprehensive ML reference library for quick lookup and review**

This section provides essential reference materials, cheat sheets, and quick guides that you'll use throughout your ML journey and career.

## 🎯 Reference Objectives

These materials help you:
- **Quick Lookup**: Find information fast during projects
- **Interview Prep**: Review key concepts before interviews
- **Daily Reference**: Access common code patterns and formulas
- **Knowledge Refresh**: Quickly review topics you've learned

## 📋 Reference Categories

### 📊 **Cheat Sheets**
**Directory**: [`cheat-sheets/`](cheat-sheets/)
- **Algorithm Comparison**: When to use which algorithm
- **Metrics Reference**: All evaluation metrics in one place
- **Python/Pandas**: Essential data manipulation commands
- **Mathematical Formulas**: Key ML mathematics
- **Hyperparameter Tuning**: Common parameter ranges

### ⚡ **Quick Guides**
**Directory**: [`quick-guides/`](quick-guides/)
- **5-Minute Tutorials**: Rapid implementation guides
- **Troubleshooting**: Common problems and solutions
- **Best Practices**: Industry-standard approaches
- **Performance Optimization**: Speed and memory tips

### 🧠 **Algorithm Summaries**
**Directory**: [`algorithm-summaries/`](algorithm-summaries/)
- **One-Page Summaries**: Each algorithm on one page
- **Pros/Cons**: Strengths and limitations
- **Use Cases**: When and where to apply
- **Implementation Notes**: Key coding considerations

### 💻 **Code Templates**
**Directory**: [`code-templates/`](code-templates/)
- **Project Structure**: Standard ML project layout
- **Common Patterns**: Frequently used code snippets
- **Deployment Templates**: API and containerization templates
- **Notebook Templates**: Structured analysis templates

## 📊 Essential Cheat Sheets

### **ML Algorithm Selection Cheat Sheet**

| Problem Type | Small Dataset (<1K) | Medium Dataset (1K-100K) | Large Dataset (>100K) |
|--------------|-------------------|------------------------|---------------------|
| **Linear Regression** | Linear Regression | Linear Regression | SGD Regressor |
| **Classification** | Naive Bayes, SVM | Random Forest, SVM | SGD Classifier, Neural Networks |
| **Clustering** | K-Means | K-Means, DBSCAN | Mini-batch K-Means |
| **Dimensionality Reduction** | PCA | PCA, t-SNE | Incremental PCA, UMAP |

### **Evaluation Metrics Quick Reference**

#### Classification Metrics
```python
# Binary Classification
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score

accuracy = accuracy_score(y_true, y_pred)
precision = precision_score(y_true, y_pred)
recall = recall_score(y_true, y_pred)
f1 = f1_score(y_true, y_pred)
auc = roc_auc_score(y_true, y_pred_proba)

# Multi-class Classification
precision_macro = precision_score(y_true, y_pred, average='macro')
recall_weighted = recall_score(y_true, y_pred, average='weighted')
```

#### Regression Metrics
```python
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

mse = mean_squared_error(y_true, y_pred)
rmse = np.sqrt(mse)
mae = mean_absolute_error(y_true, y_pred)
r2 = r2_score(y_true, y_pred)
```

### **Data Preprocessing Cheat Sheet**

```python
# Essential imports
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler, LabelEncoder
from sklearn.model_selection import train_test_split

# Data loading and basic info
df = pd.read_csv('data.csv')
df.info()
df.describe()
df.isnull().sum()

# Handling missing values
df.fillna(df.mean())  # Numerical
df.fillna(df.mode().iloc[0])  # Categorical
df.dropna()  # Remove missing

# Encoding categorical variables
le = LabelEncoder()
df['category_encoded'] = le.fit_transform(df['category'])
df_encoded = pd.get_dummies(df, columns=['category'])

# Feature scaling
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# Train-test split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
```

### **Common ML Workflow Template**

```python
# 1. Import libraries
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix

# 2. Load and explore data
df = pd.read_csv('data.csv')
print(df.info())
print(df.describe())

# 3. Preprocessing
X = df.drop('target', axis=1)
y = df['target']

# Handle missing values
X = X.fillna(X.mean())

# Encode categorical variables
X = pd.get_dummies(X)

# 4. Split data
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 5. Scale features
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# 6. Train model
model = RandomForestClassifier(random_state=42)
model.fit(X_train_scaled, y_train)

# 7. Evaluate
y_pred = model.predict(X_test_scaled)
print(classification_report(y_test, y_pred))

# 8. Cross-validation
cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=5)
print(f"CV Score: {cv_scores.mean():.3f} (+/- {cv_scores.std() * 2:.3f})")
```

## ⚡ Quick Implementation Guides

### **5-Minute Linear Regression**

```python
import numpy as np
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score

# Generate sample data
np.random.seed(42)
X = np.random.randn(100, 1)
y = 3 * X.flatten() + 2 + 0.5 * np.random.randn(100)

# Train model
model = LinearRegression()
model.fit(X, y)

# Predict and evaluate
y_pred = model.predict(X)
r2 = r2_score(y, y_pred)

# Visualize
plt.scatter(X, y, alpha=0.6)
plt.plot(X, y_pred, 'r-', linewidth=2)
plt.title(f'Linear Regression (R² = {r2:.3f})')
plt.show()

print(f"Coefficient: {model.coef_[0]:.3f}")
print(f"Intercept: {model.intercept_:.3f}")
```

### **5-Minute Classification**

```python
from sklearn.datasets import make_classification
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report

# Generate sample data
X, y = make_classification(n_samples=1000, n_features=20, n_classes=2, random_state=42)

# Split data
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Train model
model = RandomForestClassifier(random_state=42)
model.fit(X_train, y_train)

# Evaluate
y_pred = model.predict(X_test)
accuracy = accuracy_score(y_test, y_pred)

print(f"Accuracy: {accuracy:.3f}")
print("\nClassification Report:")
print(classification_report(y_test, y_pred))
```

### **5-Minute Clustering**

```python
from sklearn.datasets import make_blobs
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt

# Generate sample data
X, _ = make_blobs(n_samples=300, centers=4, cluster_std=0.60, random_state=42)

# Perform clustering
kmeans = KMeans(n_clusters=4, random_state=42)
labels = kmeans.fit_predict(X)

# Visualize
plt.scatter(X[:, 0], X[:, 1], c=labels, cmap='viridis')
plt.scatter(kmeans.cluster_centers_[:, 0], kmeans.cluster_centers_[:, 1], 
           c='red', marker='x', s=200, linewidths=3)
plt.title('K-Means Clustering')
plt.show()
```

## 🧠 Algorithm Quick Reference

### **Supervised Learning Algorithms**

| Algorithm | Type | Pros | Cons | Best For |
|-----------|------|------|------|----------|
| **Linear Regression** | Regression | Simple, interpretable, fast | Assumes linearity | Linear relationships |
| **Logistic Regression** | Classification | Probabilistic output, fast | Assumes linearity | Binary classification |
| **Decision Trees** | Both | Interpretable, handles non-linearity | Prone to overfitting | Rule-based decisions |
| **Random Forest** | Both | Robust, handles overfitting | Less interpretable | General purpose |
| **SVM** | Both | Effective in high dimensions | Slow on large datasets | High-dimensional data |
| **Neural Networks** | Both | Handles complex patterns | Requires large data | Complex patterns |

### **Unsupervised Learning Algorithms**

| Algorithm | Type | Pros | Cons | Best For |
|-----------|------|------|------|----------|
| **K-Means** | Clustering | Simple, fast | Requires k specification | Spherical clusters |
| **DBSCAN** | Clustering | Finds arbitrary shapes | Sensitive to parameters | Irregular clusters |
| **PCA** | Dimensionality Reduction | Preserves variance | Linear transformation | Feature reduction |
| **t-SNE** | Dimensionality Reduction | Great for visualization | Slow, non-deterministic | Data visualization |

## 💻 Code Templates

### **Standard ML Project Structure**

```
ml-project/
├── README.md
├── requirements.txt
├── setup.py
├── data/
│   ├── raw/
│   ├── processed/
│   └── external/
├── notebooks/
│   ├── 01-data-exploration.ipynb
│   ├── 02-data-preprocessing.ipynb
│   ├── 03-model-training.ipynb
│   └── 04-model-evaluation.ipynb
├── src/
│   ├── __init__.py
│   ├── data/
│   │   ├── __init__.py
│   │   └── make_dataset.py
│   ├── features/
│   │   ├── __init__.py
│   │   └── build_features.py
│   ├── models/
│   │   ├── __init__.py
│   │   ├── train_model.py
│   │   └── predict_model.py
│   └── visualization/
│       ├── __init__.py
│       └── visualize.py
├── models/
├── reports/
│   └── figures/
└── tests/
    ├── __init__.py
    └── test_models.py
```

### **Model Training Template**

```python
class MLModel:
    def __init__(self, model_type='random_forest'):
        self.model_type = model_type
        self.model = None
        self.scaler = StandardScaler()
        self.is_fitted = False
    
    def preprocess(self, X, fit=False):
        if fit:
            return self.scaler.fit_transform(X)
        return self.scaler.transform(X)
    
    def fit(self, X, y):
        X_scaled = self.preprocess(X, fit=True)
        
        if self.model_type == 'random_forest':
            self.model = RandomForestClassifier(random_state=42)
        elif self.model_type == 'svm':
            self.model = SVC(random_state=42)
        
        self.model.fit(X_scaled, y)
        self.is_fitted = True
        return self
    
    def predict(self, X):
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
        
        X_scaled = self.preprocess(X)
        return self.model.predict(X_scaled)
    
    def predict_proba(self, X):
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
        
        X_scaled = self.preprocess(X)
        return self.model.predict_proba(X_scaled)
```

## 🔧 Troubleshooting Guide

### **Common Issues and Solutions**

#### Memory Errors
```python
# Use chunking for large datasets
chunk_size = 10000
for chunk in pd.read_csv('large_file.csv', chunksize=chunk_size):
    process_chunk(chunk)

# Use data types optimization
df['int_column'] = df['int_column'].astype('int32')
df['float_column'] = df['float_column'].astype('float32')
```

#### Overfitting
```python
# Use cross-validation
from sklearn.model_selection import cross_val_score
scores = cross_val_score(model, X, y, cv=5)

# Add regularization
from sklearn.linear_model import Ridge
model = Ridge(alpha=1.0)

# Reduce model complexity
model = RandomForestClassifier(max_depth=5, min_samples_split=10)
```

#### Slow Training
```python
# Use sample for development
X_sample = X.sample(n=1000, random_state=42)

# Use faster algorithms
from sklearn.linear_model import SGDClassifier
model = SGDClassifier()  # Much faster than SVM

# Parallel processing
model = RandomForestClassifier(n_jobs=-1)
```

## 📖 Interview Preparation

### **Common ML Interview Questions**

1. **Explain bias-variance tradeoff**
2. **When would you use Random Forest vs SVM?**
3. **How do you handle missing data?**
4. **Explain cross-validation**
5. **What is regularization and why use it?**
6. **How do you evaluate a classification model?**
7. **Explain the difference between bagging and boosting**
8. **How do you handle imbalanced datasets?**

### **Coding Interview Template**

```python
def ml_interview_solution(data):
    """
    Template for ML coding interviews
    """
    # 1. Understand the problem
    print("Problem understanding:")
    print(f"Data shape: {data.shape}")
    print(f"Data types: {data.dtypes}")
    
    # 2. Explore the data
    print("\nData exploration:")
    print(data.describe())
    print(data.isnull().sum())
    
    # 3. Preprocess
    # Handle missing values, encode categories, scale features
    
    # 4. Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2)
    
    # 5. Train model
    model = RandomForestClassifier()
    model.fit(X_train, y_train)
    
    # 6. Evaluate
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    
    return model, accuracy
```

---

## 🎯 Using This Reference

### **Daily Workflow**
1. **Start Projects**: Use code templates
2. **Quick Lookup**: Check algorithm comparison tables
3. **Debugging**: Refer to troubleshooting guide
4. **Review**: Use cheat sheets for concept refresh

### **Interview Prep**
1. **Review**: Algorithm summaries and comparison tables
2. **Practice**: Use coding templates for mock interviews
3. **Memorize**: Key metrics and formulas
4. **Understand**: Pros/cons of each algorithm

**Remember**: These references are meant to be used frequently. Bookmark this page and refer to it often!

---

**Next**: [Career Development](../10-career-development/)
**Previous**: [Interactive Tools](../08-interactive-tools/)

*Last Updated: 2025-01-27*
