{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 📐 Linear Algebra Fundamentals for Machine Learning\n", "\n", "> **Building geometric intuition for the mathematical foundation of ML**\n", "\n", "This notebook provides hands-on exploration of linear algebra concepts crucial for machine learning. We'll build intuition through visualization and implement operations from scratch."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import seaborn as sns\n", "from typing import List, Tuple, Union\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set style for better plots\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Learning Objectives\n", "\n", "By the end of this notebook, you will:\n", "- Understand vectors geometrically and algebraically\n", "- Visualize matrix transformations in 2D and 3D\n", "- Implement core linear algebra operations from scratch\n", "- Connect linear algebra concepts to machine learning applications"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Part 1: Vectors - The Building Blocks\n", "\n", "Vectors are the fundamental units of data in machine learning. Each data point is a vector in high-dimensional space."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class Vector:\n", "    \"\"\"A simple vector class to understand vector operations\"\"\"\n", "    \n", "    def __init__(self, components: List[float]):\n", "        self.components = components\n", "        self.dimension = len(components)\n", "    \n", "    def __add__(self, other):\n", "        \"\"\"Vector addition\"\"\"\n", "        if self.dimension != other.dimension:\n", "            raise ValueError(\"Vectors must have same dimension\")\n", "        return Vector([a + b for a, b in zip(self.components, other.components)])\n", "    \n", "    def __sub__(self, other):\n", "        \"\"\"Vector subtraction\"\"\"\n", "        if self.dimension != other.dimension:\n", "            raise ValueError(\"Vectors must have same dimension\")\n", "        return Vector([a - b for a, b in zip(self.components, other.components)])\n", "    \n", "    def __mul__(self, scalar: float):\n", "        \"\"\"Scalar multiplication\"\"\"\n", "        return Vector([scalar * component for component in self.components])\n", "    \n", "    def dot(self, other):\n", "        \"\"\"Dot product\"\"\"\n", "        if self.dimension != other.dimension:\n", "            raise ValueError(\"Vectors must have same dimension\")\n", "        return sum(a * b for a, b in zip(self.components, other.components))\n", "    \n", "    def magnitude(self):\n", "        \"\"\"Vector magnitude (L2 norm)\"\"\"\n", "        return (sum(component**2 for component in self.components))**0.5\n", "    \n", "    def normalize(self):\n", "        \"\"\"Unit vector in same direction\"\"\"\n", "        mag = self.magnitude()\n", "        if mag == 0:\n", "            raise ValueError(\"Cannot normalize zero vector\")\n", "        return Vector([component / mag for component in self.components])\n", "    \n", "    def __repr__(self):\n", "        return f\"Vector({self.components})\"\n", "\n", "# Test our vector implementation\n", "v1 = Vector([3, 4])\n", "v2 = Vector([1, 2])\n", "\n", "print(f\"v1 = {v1}\")\n", "print(f\"v2 = {v2}\")\n", "print(f\"v1 + v2 = {v1 + v2}\")\n", "print(f\"v1 - v2 = {v1 - v2}\")\n", "print(f\"2 * v1 = {v1 * 2}\")\n", "print(f\"v1 · v2 = {v1.dot(v2)}\")\n", "print(f\"|v1| = {v1.magnitude():.2f}\")\n", "print(f\"v1 normalized = {v1.normalize()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 📈 Visualizing Vectors"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_vectors_2d(vectors: List[Vector], labels: List[str] = None, title: str = \"Vector Visualization\"):\n", "    \"\"\"Plot 2D vectors from origin\"\"\"\n", "    fig, ax = plt.subplots(1, 1, figsize=(10, 8))\n", "    \n", "    colors = plt.cm.Set1(np.linspace(0, 1, len(vectors)))\n", "    \n", "    for i, vector in enumerate(vectors):\n", "        if vector.dimension != 2:\n", "            continue\n", "        \n", "        x, y = vector.components\n", "        label = labels[i] if labels else f\"v{i+1}\"\n", "        \n", "        # Plot vector as arrow\n", "        ax.arrow(0, 0, x, y, head_width=0.1, head_length=0.1, \n", "                fc=colors[i], ec=colors[i], linewidth=2, label=label)\n", "        \n", "        # Add coordinates text\n", "        ax.text(x*1.1, y*1.1, f'({x:.1f}, {y:.1f})', \n", "               fontsize=10, ha='center', va='center')\n", "    \n", "    # Set equal aspect ratio and grid\n", "    ax.set_aspect('equal')\n", "    ax.grid(True, alpha=0.3)\n", "    ax.axhline(y=0, color='k', linewidth=0.5)\n", "    ax.axvline(x=0, color='k', linewidth=0.5)\n", "    \n", "    # Set limits\n", "    all_coords = [coord for v in vectors for coord in v.components]\n", "    max_coord = max(abs(coord) for coord in all_coords) * 1.2\n", "    ax.set_xlim(-max_coord, max_coord)\n", "    ax.set_ylim(-max_coord, max_coord)\n", "    \n", "    ax.set_xlabel('X')\n", "    ax.set_ylabel('Y')\n", "    ax.set_title(title)\n", "    ax.legend()\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Visualize our vectors\n", "plot_vectors_2d([v1, v2, v1 + v2], ['v1', 'v2', 'v1 + v2'], \"Vector Addition\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🧮 Exercise 1: Vector Operations\n", "\n", "**Task**: Implement and visualize the following:\n", "1. Create vectors representing data points\n", "2. Calculate distances between points\n", "3. Find the angle between vectors\n", "4. Project one vector onto another"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Exercise 1 Solution\n", "import math\n", "\n", "def vector_angle(v1: Vector, v2: Vector) -> float:\n", "    \"\"\"Calculate angle between two vectors in radians\"\"\"\n", "    dot_product = v1.dot(v2)\n", "    magnitudes = v1.magnitude() * v2.magnitude()\n", "    \n", "    if magnitudes == 0:\n", "        return 0\n", "    \n", "    cos_angle = dot_product / magnitudes\n", "    # Handle numerical errors\n", "    cos_angle = max(-1, min(1, cos_angle))\n", "    \n", "    return math.acos(cos_angle)\n", "\n", "def vector_projection(v1: Vector, v2: Vector) -> Vector:\n", "    \"\"\"Project v1 onto v2\"\"\"\n", "    if v2.magnitude() == 0:\n", "        raise ValueError(\"Cannot project onto zero vector\")\n", "    \n", "    scalar_proj = v1.dot(v2) / (v2.magnitude() ** 2)\n", "    return v2 * scalar_proj\n", "\n", "# Test the functions\n", "v1 = Vector([3, 4])\n", "v2 = Vector([1, 0])\n", "\n", "angle = vector_angle(v1, v2)\n", "projection = vector_projection(v1, v2)\n", "\n", "print(f\"Angle between v1 and v2: {math.degrees(angle):.2f} degrees\")\n", "print(f\"Projection of v1 onto v2: {projection}\")\n", "\n", "# Visualize projection\n", "plot_vectors_2d([v1, v2, projection], ['v1', 'v2', 'proj(v1 onto v2)'], \"Vector Projection\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔄 Part 2: Matrices - Linear Transformations\n", "\n", "Matrices represent linear transformations. In ML, they transform input data into new representations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class Matrix:\n", "    \"\"\"A simple matrix class for understanding matrix operations\"\"\"\n", "    \n", "    def __init__(self, data: List[List[float]]):\n", "        self.data = data\n", "        self.rows = len(data)\n", "        self.cols = len(data[0]) if data else 0\n", "        \n", "        # Validate matrix shape\n", "        for row in data:\n", "            if len(row) != self.cols:\n", "                raise ValueError(\"All rows must have same length\")\n", "    \n", "    def __getitem__(self, key):\n", "        return self.data[key]\n", "    \n", "    def __add__(self, other):\n", "        \"\"\"Matrix addition\"\"\"\n", "        if self.rows != other.rows or self.cols != other.cols:\n", "            raise ValueError(\"Matrices must have same dimensions\")\n", "        \n", "        result = []\n", "        for i in range(self.rows):\n", "            row = []\n", "            for j in range(self.cols):\n", "                row.append(self.data[i][j] + other.data[i][j])\n", "            result.append(row)\n", "        \n", "        return Matrix(result)\n", "    \n", "    def __mul__(self, other):\n", "        \"\"\"Matrix multiplication or scalar multiplication\"\"\"\n", "        if isinstance(other, (int, float)):\n", "            # Scalar multiplication\n", "            result = []\n", "            for i in range(self.rows):\n", "                row = [other * self.data[i][j] for j in range(self.cols)]\n", "                result.append(row)\n", "            return Matrix(result)\n", "        \n", "        elif isinstance(other, <PERSON>):\n", "            # Matrix multiplication\n", "            if self.cols != other.rows:\n", "                raise ValueError(f\"Cannot multiply {self.rows}x{self.cols} and {other.rows}x{other.cols} matrices\")\n", "            \n", "            result = []\n", "            for i in range(self.rows):\n", "                row = []\n", "                for j in range(other.cols):\n", "                    # Dot product of row i and column j\n", "                    dot_product = sum(self.data[i][k] * other.data[k][j] for k in range(self.cols))\n", "                    row.append(dot_product)\n", "                result.append(row)\n", "            \n", "            return Matrix(result)\n", "        \n", "        else:\n", "            raise TypeError(\"Can only multiply by scalar or matrix\")\n", "    \n", "    def transpose(self):\n", "        \"\"\"Matrix transpose\"\"\"\n", "        result = []\n", "        for j in range(self.cols):\n", "            row = [self.data[i][j] for i in range(self.rows)]\n", "            result.append(row)\n", "        return Matrix(result)\n", "    \n", "    def transform_vector(self, vector: Vector) -> Vector:\n", "        \"\"\"Apply matrix transformation to vector\"\"\"\n", "        if self.cols != vector.dimension:\n", "            raise ValueError(f\"Matrix columns ({self.cols}) must match vector dimension ({vector.dimension})\")\n", "        \n", "        result = []\n", "        for i in range(self.rows):\n", "            dot_product = sum(self.data[i][j] * vector.components[j] for j in range(self.cols))\n", "            result.append(dot_product)\n", "        \n", "        return Vector(result)\n", "    \n", "    def __repr__(self):\n", "        return f\"Matrix({self.data})\"\n", "    \n", "    def pretty_print(self):\n", "        \"\"\"Pretty print matrix\"\"\"\n", "        for row in self.data:\n", "            print(\"[\", end=\"\")\n", "            for j, val in enumerate(row):\n", "                print(f\"{val:6.2f}\", end=\"\")\n", "                if j < len(row) - 1:\n", "                    print(\", \", end=\"\")\n", "            print(\" ]\")\n", "\n", "# Test matrix operations\n", "A = Matrix([[1, 2], [3, 4]])\n", "B = Matrix([[5, 6], [7, 8]])\n", "v = Vector([1, 1])\n", "\n", "print(\"Matrix A:\")\n", "<PERSON>.pretty_print()\n", "print(\"\\nMatrix B:\")\n", "B.pretty_print()\n", "print(f\"\\nVector v: {v}\")\n", "print(f\"\\nA * v = {A.transform_vector(v)}\")\n", "print(f\"A + B =\")\n", "(A + B).pretty_print()\n", "print(f\"\\nA * B =\")\n", "(A * B).pretty_print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🎨 Visualizing Matrix Transformations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_transformation(matrix: Matrix, title: str = \"Matrix Transformation\"):\n", "    \"\"\"Visualize how a 2x2 matrix transforms the unit square\"\"\"\n", "    if matrix.rows != 2 or matrix.cols != 2:\n", "        raise ValueError(\"Only 2x2 matrices supported\")\n", "    \n", "    # Create unit square vertices\n", "    square_vertices = [\n", "        Vector([0, 0]), Vector([1, 0]), \n", "        Vector([1, 1]), Vector([0, 1]), \n", "        Vector([0, 0])  # Close the square\n", "    ]\n", "    \n", "    # Transform vertices\n", "    transformed_vertices = [matrix.transform_vector(v) for v in square_vertices]\n", "    \n", "    # Create grid points for better visualization\n", "    grid_points = []\n", "    transformed_grid = []\n", "    \n", "    for i in np.linspace(-2, 3, 6):\n", "        for j in np.linspace(-2, 3, 6):\n", "            point = Vector([i, j])\n", "            grid_points.append(point)\n", "            transformed_grid.append(matrix.transform_vector(point))\n", "    \n", "    # Plot\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # Original space\n", "    square_x = [v.components[0] for v in square_vertices]\n", "    square_y = [v.components[1] for v in square_vertices]\n", "    ax1.plot(square_x, square_y, 'b-', linewidth=3, label='Unit Square')\n", "    ax1.fill(square_x, square_y, alpha=0.3, color='blue')\n", "    \n", "    # Plot grid\n", "    grid_x = [p.components[0] for p in grid_points]\n", "    grid_y = [p.components[1] for p in grid_points]\n", "    ax1.scatter(grid_x, grid_y, alpha=0.5, s=20, color='gray')\n", "    \n", "    ax1.set_aspect('equal')\n", "    ax1.grid(True, alpha=0.3)\n", "    ax1.set_title('Original Space')\n", "    ax1.axhline(y=0, color='k', linewidth=0.5)\n", "    ax1.axvline(x=0, color='k', linewidth=0.5)\n", "    \n", "    # Transformed space\n", "    trans_square_x = [v.components[0] for v in transformed_vertices]\n", "    trans_square_y = [v.components[1] for v in transformed_vertices]\n", "    ax2.plot(trans_square_x, trans_square_y, 'r-', linewidth=3, label='Transformed Square')\n", "    ax2.fill(trans_square_x, trans_square_y, alpha=0.3, color='red')\n", "    \n", "    # Plot transformed grid\n", "    trans_grid_x = [p.components[0] for p in transformed_grid]\n", "    trans_grid_y = [p.components[1] for p in transformed_grid]\n", "    ax2.scatter(trans_grid_x, trans_grid_y, alpha=0.5, s=20, color='gray')\n", "    \n", "    ax2.set_aspect('equal')\n", "    ax2.grid(True, alpha=0.3)\n", "    ax2.set_title('Transformed Space')\n", "    ax2.axhline(y=0, color='k', linewidth=0.5)\n", "    ax2.axvline(x=0, color='k', linewidth=0.5)\n", "    \n", "    # Set equal limits for both plots\n", "    all_x = grid_x + trans_grid_x\n", "    all_y = grid_y + trans_grid_y\n", "    max_coord = max(max(abs(x) for x in all_x), max(abs(y) for y in all_y)) * 1.1\n", "    \n", "    for ax in [ax1, ax2]:\n", "        ax.set_xlim(-max_coord, max_coord)\n", "        ax.set_ylim(-max_coord, max_coord)\n", "    \n", "    plt.suptitle(f'{title}\\nMatrix: [[{matrix[0][0]:.1f}, {matrix[0][1]:.1f}], [{matrix[1][0]:.1f}, {matrix[1][1]:.1f}]]')\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Visualize different transformations\n", "transformations = [\n", "    (<PERSON>([[2, 0], [0, 1]]), \"Horizontal Scaling\"),\n", "    (<PERSON>([[1, 0.5], [0, 1]]), \"Shear Transformation\"),\n", "    (Matrix([[0, -1], [1, 0]]), \"90° Rotation\"),\n", "    (Matrix([[1, 0], [0, -1]]), \"Reflection across X-axis\")\n", "]\n", "\n", "for matrix, title in transformations:\n", "    visualize_transformation(matrix, title)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🧮 Exercise 2: Eigenvalues and Eigenvectors\n", "\n", "**Task**: Implement power iteration to find the largest eigenvalue and eigenvector"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def power_iteration(matrix: Matrix, max_iterations: int = 1000, tolerance: float = 1e-6) -> Tuple[float, Vector]:\n", "    \"\"\"Find largest eigenvalue and eigenvector using power iteration\"\"\"\n", "    if matrix.rows != matrix.cols:\n", "        raise ValueError(\"Matrix must be square\")\n", "    \n", "    # Start with random vector\n", "    np.random.seed(42)\n", "    v = Vector(np.random.randn(matrix.rows).tolist())\n", "    v = v.normalize()\n", "    \n", "    eigenvalue = 0\n", "    \n", "    for i in range(max_iterations):\n", "        # Apply matrix transformation\n", "        v_new = matrix.transform_vector(v)\n", "        \n", "        # Calculate eigenvalue (<PERSON><PERSON> quotient)\n", "        eigenvalue_new = v.dot(v_new)\n", "        \n", "        # Normalize\n", "        v_new = v_new.normalize()\n", "        \n", "        # Check convergence\n", "        if abs(eigenvalue_new - eigenvalue) < tolerance:\n", "            print(f\"Converged after {i+1} iterations\")\n", "            break\n", "        \n", "        eigenvalue = eigenvalue_new\n", "        v = v_new\n", "    \n", "    return eigenvalue, v\n", "\n", "# Test with a symmetric matrix\n", "A = Matrix([[3, 1], [1, 2]])\n", "eigenvalue, eigenvector = power_iteration(A)\n", "\n", "print(f\"Largest eigenvalue: {eigenvalue:.4f}\")\n", "print(f\"Corresponding eigenvector: {eigenvector}\")\n", "\n", "# Verify: A * v should equal λ * v\n", "Av = A.transform_vector(eigenvector)\n", "lambda_v = eigenvector * eigenvalue\n", "\n", "print(f\"\\nVerification:\")\n", "print(f\"A * v = {Av}\")\n", "print(f\"λ * v = {lambda_v}\")\n", "print(f\"Difference: {Vector([Av.components[i] - lambda_v.components[i] for i in range(len(Av.components))])}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔗 Part 3: Connections to Machine Learning\n", "\n", "Let's see how these linear algebra concepts directly apply to machine learning algorithms."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: Principal Component Analysis (PCA) using our linear algebra tools\n", "def simple_pca(data_matrix: Matrix, n_components: int = 2):\n", "    \"\"\"Simple PCA implementation using our Matrix class\"\"\"\n", "    \n", "    # Center the data (subtract mean)\n", "    # For simplicity, assume data is already centered\n", "    \n", "    # Compute covariance matrix: C = (1/n) * X^T * X\n", "    X_T = data_matrix.transpose()\n", "    covariance = X_T * data_matrix\n", "    \n", "    # Scale by 1/n\n", "    n = data_matrix.rows\n", "    covariance = covariance * (1.0 / (n - 1))\n", "    \n", "    print(\"Covariance Matrix:\")\n", "    covariance.pretty_print()\n", "    \n", "    # Find principal components (eigenvectors of covariance matrix)\n", "    eigenvalue, eigenvector = power_iteration(covariance)\n", "    \n", "    print(f\"\\nFirst Principal Component:\")\n", "    print(f\"Eigenvalue: {eigenvalue:.4f}\")\n", "    print(f\"Eigenvector: {eigenvector}\")\n", "    \n", "    return eigenvalue, eigenvector\n", "\n", "# Generate sample data\n", "np.random.seed(42)\n", "# Create correlated 2D data\n", "n_samples = 50\n", "x1 = np.random.randn(n_samples)\n", "x2 = x1 + 0.5 * np.random.randn(n_samples)  # Correlated with x1\n", "\n", "# Center the data\n", "x1 = x1 - np.mean(x1)\n", "x2 = x2 - np.mean(x2)\n", "\n", "# Create data matrix (each row is a data point)\n", "data = Matrix([[x1[i], x2[i]] for i in range(n_samples)])\n", "\n", "# Apply PCA\n", "eigenvalue, eigenvector = simple_pca(data)\n", "\n", "# Visualize\n", "plt.figure(figsize=(10, 8))\n", "plt.scatter(x1, x2, alpha=0.6, s=50)\n", "\n", "# Plot principal component direction\n", "pc_direction = eigenvector\n", "scale = 3 * np.sqrt(eigenvalue)\n", "plt.arrow(0, 0, \n", "         pc_direction.components[0] * scale, \n", "         pc_direction.components[1] * scale,\n", "         head_width=0.1, head_length=0.1, fc='red', ec='red', linewidth=3,\n", "         label=f'1st Principal Component')\n", "\n", "plt.axhline(y=0, color='k', linewidth=0.5, alpha=0.5)\n", "plt.axvline(x=0, color='k', linewidth=0.5, alpha=0.5)\n", "plt.grid(True, alpha=0.3)\n", "plt.axis('equal')\n", "plt.xlabel('Feature 1')\n", "plt.ylabel('Feature 2')\n", "plt.title('PCA: Finding Principal Components')\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\nThe first principal component captures the direction of maximum variance in the data.\")\n", "print(f\"This is exactly what PCA does - it finds the eigenvectors of the covariance matrix!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 <PERSON><PERSON><PERSON> and Key Takeaways\n", "\n", "### What We've Learned\n", "\n", "1. **Vectors**: Represent data points in high-dimensional space\n", "2. **Matrices**: Represent linear transformations of data\n", "3. **Eigenvalues/Eigenvectors**: Reveal principal directions in data (PCA)\n", "4. **Matrix Operations**: Foundation of neural network computations\n", "\n", "### ML Applications\n", "\n", "- **Linear Regression**: Uses matrix operations to solve normal equations\n", "- **Neural Networks**: Matrix multiplications in forward/backward pass\n", "- **PCA**: Eigenvalue decomposition for dimensionality reduction\n", "- **SVMs**: Kernel methods rely on dot products and transformations\n", "\n", "### Next Steps\n", "\n", "1. Practice implementing more complex matrix operations\n", "2. Explore Singular Value Decomposition (SVD)\n", "3. Study how gradients work with matrix calculus\n", "4. Connect these concepts to specific ML algorithms\n", "\n", "**Remember**: Linear algebra is the language of machine learning. The geometric intuition you've built here will make advanced topics much more accessible!"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}