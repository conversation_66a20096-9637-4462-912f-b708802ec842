# 🛠️ Complete Environment Setup Guide

> **Set up a professional ML development environment for maximum productivity**

This guide will help you create a robust, professional machine learning development environment that will serve you throughout your ML journey.

## 🎯 Setup Objectives

By the end of this setup, you will have:
- **Python Environment**: Properly configured Python with essential ML libraries
- **Development Tools**: IDE, version control, and productivity tools
- **Cloud Access**: Accounts and tools for cloud computing
- **Containerization**: Docker for reproducible environments
- **Monitoring Tools**: For tracking experiments and model performance

## 📋 Prerequisites Checklist

Before starting, ensure you have:
- [ ] Computer with at least 8GB RAM (16GB+ recommended)
- [ ] Stable internet connection
- [ ] Administrator/sudo access on your machine
- [ ] Basic command line familiarity

## 🐍 Python Environment Setup

### Step 1: Install Python 3.8+

#### Windows
```bash
# Download from python.org or use Microsoft Store
# Alternatively, use Chocolatey
choco install python
```

#### macOS
```bash
# Using Homebrew (recommended)
brew install python@3.9

# Or download from python.org
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install python3.9 python3.9-pip python3.9-venv
```

### Step 2: Install Package Managers

#### Install pip (if not included)
```bash
python -m ensurepip --upgrade
```

#### Install conda (recommended for ML)
```bash
# Download Miniconda from https://docs.conda.io/en/latest/miniconda.html
# Or Anaconda from https://www.anaconda.com/products/distribution

# Verify installation
conda --version
```

### Step 3: Create Virtual Environment

#### Using conda (recommended)
```bash
# Create environment for ML work
conda create -n ml-env python=3.9
conda activate ml-env

# Install essential packages
conda install numpy pandas matplotlib seaborn scikit-learn jupyter
conda install -c conda-forge jupyterlab
```

#### Using venv
```bash
# Create virtual environment
python -m venv ml-env

# Activate environment
# Windows:
ml-env\Scripts\activate
# macOS/Linux:
source ml-env/bin/activate

# Install essential packages
pip install -r requirements.txt
```

### Step 4: Essential Python Libraries

Create a `requirements.txt` file:

```txt
# Core Data Science
numpy>=1.21.0
pandas>=1.3.0
matplotlib>=3.4.0
seaborn>=0.11.0
plotly>=5.0.0

# Machine Learning
scikit-learn>=1.0.0
scipy>=1.7.0
statsmodels>=0.12.0

# Deep Learning
tensorflow>=2.8.0
torch>=1.11.0
torchvision>=0.12.0

# Jupyter and Development
jupyter>=1.0.0
jupyterlab>=3.0.0
ipywidgets>=7.6.0
notebook>=6.4.0

# Data Processing
openpyxl>=3.0.0
xlrd>=2.0.0
requests>=2.25.0
beautifulsoup4>=4.9.0

# Visualization
bokeh>=2.3.0
altair>=4.1.0
wordcloud>=1.8.0

# Model Deployment
flask>=2.0.0
fastapi>=0.68.0
streamlit>=1.0.0

# MLOps and Experiment Tracking
mlflow>=1.20.0
wandb>=0.12.0
tensorboard>=2.7.0

# Utilities
tqdm>=4.62.0
joblib>=1.0.0
python-dotenv>=0.19.0
click>=8.0.0

# Testing and Quality
pytest>=6.2.0
black>=21.0.0
flake8>=3.9.0
mypy>=0.910
```

Install all packages:
```bash
pip install -r requirements.txt
```

## 🔧 Development Tools Setup

### Step 1: Version Control (Git)

#### Install Git
```bash
# Windows: Download from git-scm.com
# macOS: 
brew install git
# Linux:
sudo apt install git
```

#### Configure Git
```bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
git config --global init.defaultBranch main
```

#### Create GitHub Account
1. Sign up at [github.com](https://github.com)
2. Set up SSH keys for secure access
3. Create your first repository for ML projects

### Step 2: IDE/Editor Setup

#### Option 1: VS Code (Recommended)
```bash
# Download from code.visualstudio.com

# Essential Extensions:
# - Python
# - Jupyter
# - GitLens
# - Docker
# - Remote Development
# - Python Docstring Generator
# - autoDocstring
```

#### Option 2: PyCharm
```bash
# Download PyCharm Community (free) or Professional
# Configure Python interpreter to use your ml-env
```

#### Option 3: Jupyter Lab
```bash
# Already installed in requirements.txt
jupyter lab

# Useful extensions:
pip install jupyterlab-git
pip install jupyterlab-lsp
pip install jupyter-ai
```

### Step 3: Command Line Tools

#### Install useful CLI tools
```bash
# Tree (for directory visualization)
# Windows: choco install tree
# macOS: brew install tree
# Linux: sudo apt install tree

# htop (system monitoring)
# macOS: brew install htop
# Linux: sudo apt install htop

# curl and wget (data downloading)
# Usually pre-installed, verify with:
curl --version
wget --version
```

## ☁️ Cloud Platform Setup

### Step 1: Google Cloud Platform (GCP)
```bash
# 1. Create account at cloud.google.com
# 2. Enable $300 free credit
# 3. Install gcloud CLI
curl https://sdk.cloud.google.com | bash
gcloud init

# 4. Enable useful APIs
gcloud services enable compute.googleapis.com
gcloud services enable storage.googleapis.com
gcloud services enable ml.googleapis.com
```

### Step 2: Amazon Web Services (AWS)
```bash
# 1. Create account at aws.amazon.com
# 2. Set up free tier
# 3. Install AWS CLI
pip install awscli
aws configure

# 4. Create S3 bucket for data storage
aws s3 mb s3://your-ml-bucket-name
```

### Step 3: Microsoft Azure
```bash
# 1. Create account at azure.microsoft.com
# 2. Get free credits
# 3. Install Azure CLI
# Follow instructions at docs.microsoft.com/en-us/cli/azure/install-azure-cli
az login
```

## 🐳 Docker Setup

### Step 1: Install Docker
```bash
# Download Docker Desktop from docker.com
# Follow installation instructions for your OS

# Verify installation
docker --version
docker-compose --version
```

### Step 2: Create ML Docker Environment

Create `Dockerfile`:
```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    software-properties-common \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python packages
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy application code
COPY . .

EXPOSE 8888

# Start Jupyter Lab
CMD ["jupyter", "lab", "--ip=0.0.0.0", "--port=8888", "--no-browser", "--allow-root"]
```

Create `docker-compose.yml`:
```yaml
version: '3.8'
services:
  ml-env:
    build: .
    ports:
      - "8888:8888"
      - "5000:5000"  # For Flask apps
      - "8501:8501"  # For Streamlit apps
    volumes:
      - .:/app
      - ./data:/app/data
    environment:
      - JUPYTER_ENABLE_LAB=yes
```

### Step 3: Build and Run
```bash
# Build the image
docker-compose build

# Run the environment
docker-compose up

# Access Jupyter Lab at http://localhost:8888
```

## 📊 Experiment Tracking Setup

### Step 1: MLflow
```bash
# Already installed in requirements.txt
# Start MLflow UI
mlflow ui

# Access at http://localhost:5000
```

### Step 2: Weights & Biases
```bash
# Create account at wandb.ai
# Login
wandb login

# Test setup
python -c "import wandb; wandb.init(project='test')"
```

### Step 3: TensorBoard
```bash
# Start TensorBoard
tensorboard --logdir=./logs

# Access at http://localhost:6006
```

## 🧪 Testing Your Setup

Create `test_setup.py`:
```python
#!/usr/bin/env python3
"""Test script to verify ML environment setup"""

import sys
import importlib

def test_import(module_name):
    """Test if a module can be imported"""
    try:
        importlib.import_module(module_name)
        print(f"✅ {module_name}")
        return True
    except ImportError:
        print(f"❌ {module_name}")
        return False

def main():
    print("Testing ML Environment Setup")
    print("=" * 40)
    
    # Core packages
    core_packages = [
        'numpy', 'pandas', 'matplotlib', 'seaborn', 'plotly',
        'sklearn', 'scipy', 'statsmodels',
        'tensorflow', 'torch', 'torchvision',
        'jupyter', 'jupyterlab',
        'flask', 'fastapi', 'streamlit',
        'mlflow', 'wandb'
    ]
    
    success_count = 0
    for package in core_packages:
        if test_import(package):
            success_count += 1
    
    print(f"\nResults: {success_count}/{len(core_packages)} packages available")
    
    if success_count == len(core_packages):
        print("🎉 Environment setup complete!")
    else:
        print("⚠️  Some packages missing. Check installation.")
    
    # Test basic functionality
    print("\nTesting basic functionality:")
    
    try:
        import numpy as np
        import pandas as pd
        import matplotlib.pyplot as plt
        
        # Create test data
        data = np.random.randn(100, 2)
        df = pd.DataFrame(data, columns=['A', 'B'])
        
        # Test plotting
        plt.figure(figsize=(6, 4))
        plt.scatter(df['A'], df['B'])
        plt.title('Test Plot')
        plt.savefig('test_plot.png')
        plt.close()
        
        print("✅ Basic data science workflow")
        
    except Exception as e:
        print(f"❌ Basic workflow failed: {e}")
    
    # Test ML functionality
    try:
        from sklearn.datasets import make_classification
        from sklearn.model_selection import train_test_split
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.metrics import accuracy_score
        
        # Create test dataset
        X, y = make_classification(n_samples=1000, n_features=20, random_state=42)
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # Train model
        model = RandomForestClassifier(random_state=42)
        model.fit(X_train, y_train)
        
        # Evaluate
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"✅ ML workflow (accuracy: {accuracy:.3f})")
        
    except Exception as e:
        print(f"❌ ML workflow failed: {e}")

if __name__ == "__main__":
    main()
```

Run the test:
```bash
python test_setup.py
```

## 🔧 Troubleshooting Common Issues

### Issue 1: Package Installation Failures
```bash
# Update pip
python -m pip install --upgrade pip

# Clear pip cache
pip cache purge

# Install with no cache
pip install --no-cache-dir package_name
```

### Issue 2: Jupyter Kernel Issues
```bash
# Install kernel for your environment
python -m ipykernel install --user --name=ml-env

# List available kernels
jupyter kernelspec list

# Remove problematic kernel
jupyter kernelspec uninstall unwanted-kernel
```

### Issue 3: Memory Issues
```bash
# Increase virtual memory (Linux/macOS)
sudo sysctl -w vm.max_map_count=262144

# Monitor memory usage
htop  # or top on macOS
```

### Issue 4: Permission Issues
```bash
# Fix pip permissions (macOS/Linux)
pip install --user package_name

# Or use conda instead
conda install package_name
```

## 📚 Additional Resources

### Documentation
- [Python.org](https://docs.python.org/3/)
- [Conda Documentation](https://docs.conda.io/)
- [Docker Documentation](https://docs.docker.com/)
- [Git Documentation](https://git-scm.com/doc)

### Learning Resources
- [Python for Data Science Handbook](https://jakevdp.github.io/PythonDataScienceHandbook/)
- [Scikit-learn Documentation](https://scikit-learn.org/stable/)
- [TensorFlow Tutorials](https://www.tensorflow.org/tutorials)
- [PyTorch Tutorials](https://pytorch.org/tutorials/)

### Community
- [Stack Overflow](https://stackoverflow.com/questions/tagged/machine-learning)
- [Reddit r/MachineLearning](https://www.reddit.com/r/MachineLearning/)
- [Kaggle Learn](https://www.kaggle.com/learn)
- [Papers with Code](https://paperswithcode.com/)

---

## ✅ Setup Complete!

Congratulations! You now have a professional ML development environment. You're ready to start your machine learning journey!

**Next Step**: Begin with [Week 1: Mathematical Foundations](../01-mathematics/)

---

*Last Updated: 2025-01-27*
