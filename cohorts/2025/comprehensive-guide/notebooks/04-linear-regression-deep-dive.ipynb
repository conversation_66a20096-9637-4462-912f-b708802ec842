{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 📈 Linear Regression Deep Dive\n", "\n", "> **Master linear regression from theory to implementation**\n", "\n", "This notebook provides a comprehensive exploration of linear regression, from mathematical foundations to practical implementation. You'll build everything from scratch and compare with scikit-learn.\n", "\n", "## 🎯 Learning Objectives\n", "\n", "By the end of this notebook, you will:\n", "- **Understand** the mathematical foundation of linear regression\n", "- **Implement** linear regression from scratch using NumPy\n", "- **Master** feature engineering and data preprocessing\n", "- **Evaluate** model performance using multiple metrics\n", "- **Compare** different optimization approaches\n", "- **Apply** regularization techniques (Ridge, Lasso)\n", "\n", "## 📊 Dataset: Boston Housing (Synthetic)\n", "\n", "We'll use a synthetic version of the Boston Housing dataset to predict house prices based on various features.\n", "\n", "**Features:**\n", "- `CRIM`: Crime rate per capita\n", "- `ZN`: Proportion of residential land zoned for lots over 25,000 sq.ft\n", "- `INDUS`: Proportion of non-retail business acres\n", "- `CHAS`: Charles River dummy variable\n", "- `NOX`: Nitric oxides concentration\n", "- `RM`: Average number of rooms per dwelling\n", "- `AGE`: Proportion of owner-occupied units built prior to 1940\n", "- `DIS`: Weighted distances to employment centers\n", "- `RAD`: Index of accessibility to radial highways\n", "- `TAX`: Property tax rate\n", "- `PTRATIO`: Pupil-teacher ratio\n", "- `B`: Proportion of blacks by town\n", "- `LSTAT`: % lower status of the population\n", "\n", "**Target:** `MEDV` - Median value of homes in $1000s"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🛠️ Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Essential imports\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.datasets import make_regression\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.linear_model import LinearRegression, Ridge, Lasso\n", "from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set style for better plots\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "# Set random seed for reproducibility\n", "np.random.seed(42)\n", "\n", "print(\"✅ All imports successful!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Data Generation and Exploration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate synthetic housing data\n", "X, y = make_regression(\n", "    n_samples=506,\n", "    n_features=13,\n", "    noise=10,\n", "    random_state=42\n", ")\n", "\n", "# Create feature names\n", "feature_names = [\n", "    'CRIM', 'ZN', 'INDUS', 'CHAS', 'NOX', 'RM', 'AGE',\n", "    'DIS', 'RAD', 'TAX', 'PTRATIO', 'B', 'LSTAT'\n", "]\n", "\n", "# Create DataFrame\n", "df = pd.DataFrame(X, columns=feature_names)\n", "df['MEDV'] = y\n", "\n", "# Scale target to realistic house prices (in thousands)\n", "df['MEDV'] = (df['MEDV'] - df['MEDV'].min()) / (df['MEDV'].max() - df['MEDV'].min()) * 40 + 10\n", "\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(f\"Features: {len(feature_names)}\")\n", "print(f\"Target range: ${df['MEDV'].min():.1f}k - ${df['MEDV'].max():.1f}k\")\n", "\n", "# Display first few rows\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Comprehensive data exploration\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Target distribution\n", "axes[0, 0].hist(df['MEDV'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')\n", "axes[0, 0].set_title('Distribution of House Prices')\n", "axes[0, 0].set_xlabel('Price ($1000s)')\n", "axes[0, 0].set_ylabel('Frequency')\n", "\n", "# Correlation heatmap\n", "corr_matrix = df.corr()\n", "mask = np.triu(np.ones_like(corr_matrix, dtype=bool))\n", "sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,\n", "            square=True, ax=axes[0, 1], cbar_kws={\"shrink\": .8})\n", "axes[0, 1].set_title('Feature Correlation Matrix')\n", "\n", "# Feature vs target scatter (strongest correlation)\n", "strongest_corr_feature = corr_matrix['MEDV'].abs().sort_values(ascending=False).index[1]\n", "axes[1, 0].scatter(df[strongest_corr_feature], df['MEDV'], alpha=0.6)\n", "axes[1, 0].set_xlabel(strongest_corr_feature)\n", "axes[1, 0].set_ylabel('House Price ($1000s)')\n", "axes[1, 0].set_title(f'Price vs {strongest_corr_feature} (Strongest Correlation)')\n", "\n", "# Box plot of target\n", "axes[1, 1].boxplot(df['MEDV'])\n", "axes[1, 1].set_ylabel('House Price ($1000s)')\n", "axes[1, 1].set_title('House Price Distribution (Box Plot)')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Summary statistics\n", "print(\"\\n📊 Summary Statistics:\")\n", "print(df.describe().round(2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧮 Mathematical Foundation\n", "\n", "### Linear Regression Equation\n", "\n", "The linear regression model can be expressed as:\n", "\n", "$$\\hat{y} = \\beta_0 + \\beta_1 x_1 + \\beta_2 x_2 + ... + \\beta_n x_n$$\n", "\n", "In matrix form:\n", "$$\\hat{y} = X\\beta$$\n", "\n", "### Normal Equation\n", "\n", "The optimal parameters can be found using:\n", "$$\\beta = (X^T X)^{-1} X^T y$$\n", "\n", "### Cost Function (Mean Squared Error)\n", "\n", "$$J(\\beta) = \\frac{1}{2m} \\sum_{i=1}^{m} (h_\\beta(x^{(i)}) - y^{(i)})^2$$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔨 Linear Regression from Scratch"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class LinearRegressionFromScratch:\n", "    def __init__(self, learning_rate=0.01, max_iterations=1000, tolerance=1e-6):\n", "        self.learning_rate = learning_rate\n", "        self.max_iterations = max_iterations\n", "        self.tolerance = tolerance\n", "        self.weights = None\n", "        self.bias = None\n", "        self.cost_history = []\n", "        \n", "    def add_bias_term(self, X):\n", "        \"\"\"Add bias term (column of ones) to feature matrix\"\"\"\n", "        return np.column_stack([np.ones(X.shape[0]), X])\n", "    \n", "    def compute_cost(self, X, y, weights):\n", "        \"\"\"Compute mean squared error cost\"\"\"\n", "        m = X.shape[0]\n", "        predictions = X @ weights\n", "        cost = (1 / (2 * m)) * np.sum((predictions - y) ** 2)\n", "        return cost\n", "    \n", "    def fit_normal_equation(self, X, y):\n", "        \"\"\"Fit using normal equation (analytical solution)\"\"\"\n", "        X_with_bias = self.add_bias_term(X)\n", "        \n", "        # Normal equation: θ = (X^T X)^(-1) X^T y\n", "        try:\n", "            self.weights = np.linalg.inv(X_with_bias.T @ X_with_bias) @ X_with_bias.T @ y\n", "        except np.linalg.LinAlgError:\n", "            # Use pseudo-inverse if matrix is singular\n", "            self.weights = np.linalg.pinv(X_with_bias.T @ X_with_bias) @ X_with_bias.T @ y\n", "        \n", "        self.bias = self.weights[0]\n", "        self.weights = self.weights[1:]\n", "        \n", "        # Calculate final cost\n", "        final_cost = self.compute_cost(X_with_bias, y, \n", "                                     np.concatenate([[self.bias], self.weights]))\n", "        self.cost_history = [final_cost]\n", "        \n", "        return self\n", "    \n", "    def fit_gradient_descent(self, X, y):\n", "        \"\"\"Fit using gradient descent\"\"\"\n", "        m, n = X.shape\n", "        \n", "        # Initialize parameters\n", "        self.weights = np.random.normal(0, 0.01, n)\n", "        self.bias = 0\n", "        self.cost_history = []\n", "        \n", "        for i in range(self.max_iterations):\n", "            # Forward pass\n", "            predictions = X @ self.weights + self.bias\n", "            \n", "            # Compute cost\n", "            cost = (1 / (2 * m)) * np.sum((predictions - y) ** 2)\n", "            self.cost_history.append(cost)\n", "            \n", "            # Compute gradients\n", "            dw = (1 / m) * X.T @ (predictions - y)\n", "            db = (1 / m) * np.sum(predictions - y)\n", "            \n", "            # Update parameters\n", "            self.weights -= self.learning_rate * dw\n", "            self.bias -= self.learning_rate * db\n", "            \n", "            # Check for convergence\n", "            if i > 0 and abs(self.cost_history[-2] - self.cost_history[-1]) < self.tolerance:\n", "                print(f\"Converged after {i+1} iterations\")\n", "                break\n", "        \n", "        return self\n", "    \n", "    def predict(self, X):\n", "        \"\"\"Make predictions\"\"\"\n", "        if self.weights is None:\n", "            raise ValueError(\"Model not fitted yet. Call fit() first.\")\n", "        return X @ self.weights + self.bias\n", "    \n", "    def score(self, X, y):\n", "        \"\"\"Calculate R² score\"\"\"\n", "        predictions = self.predict(X)\n", "        ss_res = np.sum((y - predictions) ** 2)\n", "        ss_tot = np.sum((y - np.mean(y)) ** 2)\n", "        return 1 - (ss_res / ss_tot)\n", "    \n", "    def plot_cost_history(self):\n", "        \"\"\"Plot cost function over iterations\"\"\"\n", "        if len(self.cost_history) > 1:\n", "            plt.figure(figsize=(10, 6))\n", "            plt.plot(self.cost_history)\n", "            plt.title('Cost Function Over Iterations')\n", "            plt.xlabel('Iteration')\n", "            plt.ylabel('Cost (MSE)')\n", "            plt.grid(True)\n", "            plt.show()\n", "        else:\n", "            print(\"Cost history not available (normal equation used)\")\n", "\n", "print(\"✅ LinearRegressionFromScratch class defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔄 Data Preprocessing and Model Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare features and target\n", "X = df[feature_names].values\n", "y = df['MEDV'].values\n", "\n", "# Split the data\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42\n", ")\n", "\n", "# Scale the features\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "print(f\"Training set: {X_train_scaled.shape}\")\n", "print(f\"Test set: {X_test_scaled.shape}\")\n", "print(f\"Target range - Train: [{y_train.min():.1f}, {y_train.max():.1f}]\")\n", "print(f\"Target range - Test: [{y_test.min():.1f}, {y_test.max():.1f}]\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train models using different approaches\n", "print(\"🔨 Training Linear Regression Models...\\n\")\n", "\n", "# 1. Our implementation with Normal Equation\n", "print(\"1️⃣ Training with Normal Equation...\")\n", "lr_normal = LinearRegressionFromScratch()\n", "lr_normal.fit_normal_equation(X_train_scaled, y_train)\n", "print(f\"   Final cost: {lr_normal.cost_history[-1]:.4f}\")\n", "\n", "# 2. Our implementation with Gradient Descent\n", "print(\"\\n2️⃣ Training with Gradient Descent...\")\n", "lr_gd = LinearRegressionFromScratch(learning_rate=0.01, max_iterations=1000)\n", "lr_gd.fit_gradient_descent(X_train_scaled, y_train)\n", "print(f\"   Final cost: {lr_gd.cost_history[-1]:.4f}\")\n", "\n", "# 3. Scikit-learn implementation\n", "print(\"\\n3️⃣ Training with Scikit-learn...\")\n", "lr_sklearn = LinearRegression()\n", "lr_sklearn.fit(X_train_scaled, y_train)\n", "sklearn_predictions = lr_sklearn.predict(X_train_scaled)\n", "sklearn_cost = mean_squared_error(y_train, sklearn_predictions) / 2\n", "print(f\"   Training cost: {sklearn_cost:.4f}\")\n", "\n", "print(\"\\n✅ All models trained successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize gradient descent convergence\n", "lr_gd.plot_cost_history()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Model Evaluation and Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Make predictions on test set\n", "pred_normal = lr_normal.predict(X_test_scaled)\n", "pred_gd = lr_gd.predict(X_test_scaled)\n", "pred_sklearn = lr_sklearn.predict(X_test_scaled)\n", "\n", "# Calculate metrics for all models\n", "def calculate_metrics(y_true, y_pred, model_name):\n", "    mse = mean_squared_error(y_true, y_pred)\n", "    rmse = np.sqrt(mse)\n", "    mae = mean_absolute_error(y_true, y_pred)\n", "    r2 = r2_score(y_true, y_pred)\n", "    \n", "    return {\n", "        'Model': model_name,\n", "        'MSE': mse,\n", "        'RMSE': rmse,\n", "        'MAE': mae,\n", "        'R²': r2\n", "    }\n", "\n", "# Compare all models\n", "results = [\n", "    calculate_metrics(y_test, pred_normal, 'Normal Equation'),\n", "    calculate_metrics(y_test, pred_gd, 'Gradient Descent'),\n", "    calculate_metrics(y_test, pred_sklearn, 'Scikit-learn')\n", "]\n", "\n", "results_df = pd.DataFrame(results)\n", "print(\"📊 Model Comparison Results:\")\n", "print(results_df.round(4))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize predictions vs actual values\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 5))\n", "\n", "models = [\n", "    ('Normal Equation', pred_normal),\n", "    ('Gradient Descent', pred_gd),\n", "    ('Scikit-learn', pred_sklearn)\n", "]\n", "\n", "for i, (name, predictions) in enumerate(models):\n", "    axes[i].scatter(y_test, predictions, alpha=0.6)\n", "    axes[i].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)\n", "    axes[i].set_xlabel('Actual Prices')\n", "    axes[i].set_ylabel('Predicted Prices')\n", "    axes[i].set_title(f'{name}\\nR² = {r2_score(y_test, predictions):.4f}')\n", "    axes[i].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Practice Problems\n", "\n", "Now it's your turn to practice! Complete the following exercises:\n", "\n", "### **Problem 1: Feature Importance Analysis**\n", "Analyze which features are most important for predicting house prices. Create a visualization showing feature coefficients."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Your code here\n", "# Hint: Use the weights from your trained model\n", "# Create a bar plot showing feature importance\n", "\n", "# Solution template:\n", "def analyze_feature_importance(model, feature_names):\n", "    \"\"\"\n", "    Analyze and visualize feature importance\n", "    \n", "    Parameters:\n", "    model: trained linear regression model\n", "    feature_names: list of feature names\n", "    \"\"\"\n", "    # TODO: Extract coefficients\n", "    # TODO: Create visualization\n", "    # TODO: Interpret results\n", "    pass\n", "\n", "# Call your function\n", "# analyze_feature_importance(lr_normal, feature_names)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### **Problem 2: Regularization Implementation**\n", "Implement Ridge regression from scratch and compare it with regular linear regression."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Your code here\n", "# Implement Ridge regression with L2 regularization\n", "\n", "class RidgeRegressionFromScratch:\n", "    def __init__(self, alpha=1.0):\n", "        self.alpha = alpha  # Regularization strength\n", "        self.weights = None\n", "        self.bias = None\n", "    \n", "    def fit(self, X, y):\n", "        \"\"\"\n", "        Fit Ridge regression using normal equation with regularization\n", "        θ = (X^T X + αI)^(-1) X^T y\n", "        \"\"\"\n", "        # TODO: Implement Ridge regression\n", "        # Hint: Add alpha * I to X^T X before inversion\n", "        pass\n", "    \n", "    def predict(self, X):\n", "        # TODO: Implement prediction\n", "        pass\n", "\n", "# Test your implementation\n", "# ridge_model = RidgeRegressionFromScratch(alpha=1.0)\n", "# ridge_model.fit(X_train_scaled, y_train)\n", "# ridge_predictions = ridge_model.predict(X_test_scaled)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### **Problem 3: Learning Rate Experiment**\n", "Experiment with different learning rates and visualize how they affect convergence."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Your code here\n", "# Test learning rates: [0.001, 0.01, 0.1, 1.0]\n", "# Plot cost history for each learning rate\n", "\n", "learning_rates = [0.001, 0.01, 0.1, 1.0]\n", "\n", "# TODO: Train models with different learning rates\n", "# TODO: Plot cost histories on the same graph\n", "# TODO: Analyze which learning rate works best"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Key Takeaways\n", "\n", "From this notebook, you should understand:\n", "\n", "1. **Mathematical Foundation**: Linear regression finds the best linear relationship between features and target\n", "2. **Implementation Methods**: Both analytical (normal equation) and iterative (gradient descent) approaches work\n", "3. **Feature Scaling**: Essential for gradient descent to converge properly\n", "4. **Model Evaluation**: Multiple metrics (MSE, RMSE, MAE, R²) provide different insights\n", "5. **Regularization**: Helps prevent overfitting in complex models\n", "\n", "## 🔗 Next Steps\n", "\n", "1. **Complete the practice problems** above\n", "2. **Try polynomial features** to capture non-linear relationships\n", "3. **Experiment with different datasets** from scikit-learn\n", "4. **Move to the next notebook**: Logistic Regression Deep Dive\n", "\n", "## 📚 Additional Resources\n", "\n", "- [Scikit-learn Linear Models](https://scikit-learn.org/stable/modules/linear_model.html)\n", "- [<PERSON>'s ML Course](https://www.coursera.org/learn/machine-learning)\n", "- [Elements of Statistical Learning](https://web.stanford.edu/~hastie/ElemStatLearn/)\n", "\n", "---\n", "\n", "**Great job completing this notebook!** 🎉\n", "\n", "You've built linear regression from scratch and understand both the theory and implementation. This foundation will serve you well as you progress to more advanced algorithms."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}