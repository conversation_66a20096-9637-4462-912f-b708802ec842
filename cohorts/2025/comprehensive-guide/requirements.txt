# Core ML and Data Science Libraries
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0
scipy>=1.7.0

# Visualization
matplotlib>=3.4.0
seaborn>=0.11.0
plotly>=5.0.0

# Jupyter and Notebook Environment
jupyter>=1.0.0
jupyterlab>=3.0.0
ipywidgets>=7.6.0
notebook>=6.4.0

# Deep Learning
tensorflow>=2.8.0
keras>=2.8.0
torch>=1.10.0
torchvision>=0.11.0

# Advanced ML Libraries
xgboost>=1.5.0
lightgbm>=3.3.0
catboost>=1.0.0
optuna>=2.10.0

# Feature Engineering and Preprocessing
feature-engine>=1.4.0
category-encoders>=2.3.0
imbalanced-learn>=0.8.0

# Model Interpretation
shap>=0.40.0
lime>=0.2.0
eli5>=0.11.0

# Web Development and APIs
flask>=2.0.0
fastapi>=0.70.0
uvicorn>=0.15.0
requests>=2.25.0
streamlit>=1.2.0

# Cloud and Deployment
boto3>=1.20.0
google-cloud-storage>=2.0.0
azure-storage-blob>=12.8.0
docker>=5.0.0

# Data Processing and Storage
sqlalchemy>=1.4.0
pymongo>=4.0.0
redis>=4.0.0
psycopg2-binary>=2.9.0

# Monitoring and Logging
mlflow>=1.20.0
wandb>=0.12.0
tensorboard>=2.8.0

# Utilities
tqdm>=4.62.0
joblib>=1.1.0
pickle5>=0.0.11
python-dotenv>=0.19.0
pyyaml>=6.0

# Testing and Quality
pytest>=6.2.0
pytest-cov>=3.0.0
black>=21.9.0
flake8>=4.0.0
mypy>=0.910

# Jupyter Extensions
jupyter-contrib-nbextensions>=0.5.1
jupyter-nbextensions-configurator>=0.4.1

# Additional Visualization
wordcloud>=1.8.0
folium>=0.12.0
bokeh>=2.4.0

# Time Series
statsmodels>=0.13.0
prophet>=1.0.0
pmdarima>=1.8.0

# Natural Language Processing
nltk>=3.6.0
spacy>=3.4.0
transformers>=4.15.0
gensim>=4.1.0

# Computer Vision
opencv-python>=4.5.0
pillow>=8.3.0
albumentations>=1.1.0

# Hyperparameter Tuning
hyperopt>=0.2.5
scikit-optimize>=0.9.0
ray[tune]>=1.9.0

# Distributed Computing
dask>=2021.10.0
modin>=0.12.0

# Development Tools
ipython>=7.28.0
ipdb>=0.13.0
memory-profiler>=0.60.0
line-profiler>=3.3.0
