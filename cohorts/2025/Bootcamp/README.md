## Machine Learning Zoomcamp 2025

* [Pre-course Q&A session](https://www.youtube.com/watch?v=ph1PxZIkz1o)
* [Launch stream with course overview](https://www.youtube.com/watch?v=z064DoidiKg)
* [Course calendar](https://calendar.google.com/calendar/u/0/r?cid=**********************************************************************&pli=1) ([Google calendar syncselect](https://www.google.com/calendar/syncselect) and for Apple Calendar on macOS/iPhone/iPad see this [help](https://support.google.com/calendar/answer/99358?hl=en&co=GENIE.Platform=Desktop))
* [Course management platform](https://courses.datatalks.club/ml-zoomcamp-2025/)
* [FAQ](https://docs.google.com/document/d/1LpPanc33QJJ6BSsyxVg-pWNMplal84TdZtq10naIhD8/edit#)
* [Course Playlist: Only 2025 Live videos & homeworks](https://www.youtube.com/playlist?list=PL3MmuxUbc_hJoui-E7wf2r5wWgET3MMZt)
* [Leaderboard](https://courses.datatalks.club/ml-zoomcamp-2025/leaderboard)

### Syllabus

[**1. Introduction to Machine Learning**](01-intro/)

* [Module materials](../../01-intro)
* [Homework](01-intro/homework.md)

[**2. Machine Learning for Regression**](02-regression/)

* [Module materials](../../02-regression)
* [Homework](02-regression/homework.md)

[**3. Machine Learning for Classification**](03-classification/)

* [Module materials](../../03-classification)
* [Homework](03-classification/homework.md)

[**4. Evaluation Metrics for Classification**](04-evaluation/)

* [Module materials](../../04-evaluation)
* [Homework](04-evaluation/homework.md)

[**5. Deploying Machine Learning Models**](05-deployment/)

* [Module materials](../../05-deployment)
* [Homework](05-deployment/homework.md)

[**6. Decision Trees and Ensemble Learning**](06-trees/)

* [Module materials](../../06-trees)
* [Homework](06-trees/homework.md)

[**Midterm Project**](projects.md#midterm-project)

* More information: [projects.md](projects.md#midterm-project)


[**8. Neural Networks and Deep Learning**](08-deep-learning/)

* [Module materials](../../08-deep-learning)
* [Homework](08-deep-learning/homework.md)


[**9. Serverless Deep Learning**](09-serverless/)

* [Module materials](../../09-serverless)
* [Homework](09-serverless/homework.md)


[**10. Kubernetes and TensorFlow Serving**](10-kubernetes/)

* [Module materials](../../10-kubernetes)
* [Homework](10-kubernetes/homework.md)


[**Capstone Project**](projects.md#capstone-1)

* More information: [projects.md](projects.md#capstone-1)


[**Capstone Project 2**](projects.md#capstone-2)

* More information: [projects.md](projects.md#capstone-2)


**[Article](article.md) (Optional)**

* More information: [article.md](article.md)

