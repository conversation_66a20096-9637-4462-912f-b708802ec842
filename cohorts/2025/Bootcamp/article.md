## Article (optional)

The best way to learn about something - to teach it.

In this part of the course, we'll explore some topics that weren't 
covered in the course.

You'll need to learn about this topic and then write about what
you learned in an article.


[More information about articles](../../article/README.md)

## Submitting the results

* Submit your article to [`#course-ml-zoomcamp-articles`](https://app.slack.com/client/T01ATQK62F8/C02QXBFS1PU) - 
  just share the link to your article there
* Submit the same link to [this form](https://courses.datatalks.club/ml-zoomcamp-2024/homework/article) so we could link it to your message from the channel

## Evaluating

We'll use voting for scoring your articles.

* Check the articles in the [`#course-ml-zoomcamp-articles`](https://app.slack.com/client/T01ATQK62F8/C02QXBFS1PU) channel and put a :+1: reaction to articles that you liked
* The top voted articles will get 20 points 


## Deadline

The deadline for finishing the article is 31 January


## Articles from 2024


* [You will like my offer](https://markogolovko.com/blog/you-will-like-my-offer/) by <PERSON><PERSON>
* [Natural Language Processing using spaCy, TensorFlow and BERT model architecture](https://volcano-camp-325.notion.site/Natural-Language-Processing-using-spaCy-TensorFlow-and-BERT-model-architecture-1895067176b380d09484d4b0338b0c5e?pvs=4) by Alexander Daniel Rios
* [Setting up the environments for ML Zoomcamp 2024 - macOS](https://medium.com/@till.meineke/setting-up-the-environments-for-ml-zoomcamp-2024-eceb6e42e36e) by Till Meineke


### Past articles

See examples of articles from the previous cohorts [here](../../article/README.md)
